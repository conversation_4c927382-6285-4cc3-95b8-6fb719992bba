<template>
  <div class="original-container">
    <!-- 左侧内容区域 -->
    <div class="left-column">
      <!-- 顶部标题 -->
      <div class="page-header">
        <h1>AI 文案生成</h1>
        <div class="header-actions">
          <el-button type="primary" text @click="handleClear">清除</el-button>
        </div>
      </div>

      <!-- 发布渠道选择 -->
      <div class="form-section">
        <div class="section-title">发布渠道</div>
        <div class="channel-tabs">
          <el-button
            v-for="channel in channels"
            :key="channel.key"
            :type="activeChannel === channel.key ? 'primary' : 'default'"
            :plain="activeChannel !== channel.key"
            size="small"
            @click="activeChannel = channel.key"
          >
            <Icon :icon="channel.icon" class="mr-1" />
            {{ channel.label }}
          </el-button>
        </div>
      </div>

      <!-- 详细信息表单 -->
      <div class="form-section">
        <div class="section-title">详细信息</div>
        <div class="form-content">
          <!-- 产品 -->
          <div class="form-item">
            <label class="form-label">产品：</label>
            <el-input v-model="formData.product" placeholder="请输入产品名称" class="form-input" />
          </div>

          <!-- 客户 -->
          <div class="form-item">
            <label class="form-label">客户：</label>
            <el-input v-model="formData.customer" placeholder="请输入目标客户" class="form-input" />
          </div>

          <!-- 其他 -->
          <div class="form-item">
            <label class="form-label">其他：</label>
            <el-input
              v-model="formData.other"
              type="textarea"
              :rows="3"
              placeholder="请输入其他要求或描述"
              class="form-input"
            />
          </div>

          <!-- 热词 -->
          <div class="form-item">
            <label class="form-label">热词：</label>
            <el-input v-model="formData.keywords" placeholder="请输入相关热词" class="form-input" />
          </div>

          <!-- 字数 -->
          <div class="form-item">
            <label class="form-label">字数：</label>
            <el-input
              v-model="formData.wordCount"
              placeholder="120"
              class="form-input word-count-input"
            />
          </div>
        </div>
      </div>

      <!-- 生成按钮 -->
      <div class="generate-section">
        <el-button
          type="primary"
          size="large"
          :loading="isGenerating"
          @click="handleGenerate"
          class="generate-btn"
        >
          <Icon icon="ep:magic-stick" class="mr-2" />
          {{ isGenerating ? '生成中' : '生成' }}
        </el-button>
      </div>

      <!-- AI 原创内容展示 -->
      <div v-if="generatedContent" class="content-section">
        <div class="section-header">
          <div class="section-title">AI 原创</div>
          <div class="section-actions">
            <el-button type="primary" text @click="handleManage">
              <Icon icon="ep:user" class="mr-1" />
              人员管理
            </el-button>
            <el-button type="primary" text @click="handleCopy"> 复制 </el-button>
          </div>
        </div>

        <div class="content-display">
          <div class="content-header">
            <span class="content-title">标题：</span>
            <span class="highlight-text">{{ generatedContent.title }}</span>
          </div>
          <div class="content-body">
            <div class="content-text">{{ generatedContent.content }}</div>
          </div>
        </div>

        <!-- 立即生成按钮 -->
        <div class="quick-generate">
          <el-button
            type="primary"
            size="large"
            @click="handleQuickGenerate"
            class="quick-generate-btn"
          >
            立即生成 + 3
          </el-button>
        </div>
      </div>
    </div>

    <!-- 右侧AI聊天组件 -->
    <div class="right-column">
      <AiChatWidget
        title="AI设计师"
        welcome-message="Hi，我是你的AI设计师，让我们开始今天的创作吧！"
        :suggestions="aiSuggestions"
        placeholder="在这里输入问题"
        @message-sent="handleMessageSent"
        @message-received="handleMessageReceived"
        @error="handleChatError"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import AiChatWidget from '@/components/AiChatWidget/index.vue'

defineOptions({ name: 'CreationOriginal' })

// 发布渠道数据
const channels = ref([
  { key: 'douyin', label: '抖音', icon: 'ep:video-camera' },
  { key: 'xiaohongshu', label: '小红书', icon: 'ep:picture' }
])

// 当前选中的渠道
const activeChannel = ref('douyin')

// 表单数据
const formData = reactive({
  product: '耳机',
  customer: '上班族',
  other: '在地铁站中的使用场景，降噪',
  keywords: '耳机降噪',
  wordCount: '120'
})

// 生成状态
const isGenerating = ref(false)

// 生成的内容
const generatedContent = ref<{
  title: string
  content: string
} | null>(null)

// AI建议问题
const aiSuggestions = ref(['帮我提写营销文案', '科技公司品牌logo设计', '游戏图标设计'])

// 生成内容
const handleGenerate = async () => {
  if (!formData.product.trim()) {
    ElMessage.warning('请输入产品名称')
    return
  }

  isGenerating.value = true

  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 模拟生成的内容
    generatedContent.value = {
      title: '2025，未来已来！你准备好了吗？✨',
      content: `时间定格在这一刻——2025年7月22日16:03，世界正以前所未有的速度向前奔跑。
🚀 科技革新，重塑生活。
🎯 智能未来，触手可及。
🔥 你的每一步，都在定义未来的轨迹。
别让时间白白流逝，现在就是最好的开始！
🌟 拥抱改变，创造无限可能！🌟
#未来已来 #2025新元年 #改变从现在开始`
    }
  } catch (error) {
    ElMessage.error('生成失败，请重试')
  } finally {
    isGenerating.value = false
  }
}

// 快速生成
const handleQuickGenerate = () => {
  handleGenerate()
}

// 复制内容
const handleCopy = async () => {
  if (!generatedContent.value) return

  try {
    const textToCopy = `${generatedContent.value.title}\n\n${generatedContent.value.content}`
    await navigator.clipboard.writeText(textToCopy)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 清除表单
const handleClear = () => {
  formData.product = ''
  formData.customer = ''
  formData.other = ''
  formData.keywords = ''
  formData.wordCount = '120'
  generatedContent.value = null
  ElMessage.success('已清除所有内容')
}

// 人员管理
const handleManage = () => {
  ElMessage.info('人员管理功能开发中')
}

// AI聊天事件处理
const handleMessageSent = (message: string) => {
  console.log('消息已发送:', message)
}

const handleMessageReceived = (message: string) => {
  console.log('收到回复:', message)
}

const handleChatError = (error: Error) => {
  console.error('聊天错误:', error)
  ElMessage.error(`聊天出错: ${error.message}`)
}
</script>

<style lang="scss" scoped>
// 响应式设计
@media (width <= 1200px) {
  .right-column {
    width: 350px;
  }
}

@media (width <= 768px) {
  .original-container {
    flex-direction: column;
    height: auto;
  }

  .right-column {
    width: 100%;
    height: 400px;
  }

  .left-column {
    padding: 16px;
  }
}

.original-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 60px); // 减去顶部导航高度
  padding: 20px;
  overflow: hidden;
  background: var(--app-content-bg-color, #f5f5f5);
  gap: 20px;
}

.left-column {
  display: flex;
  padding-right: 10px; // 为滚动条留出空间
  overflow-y: auto; // 左侧可滚动
  background: transparent;
  flex: 1;
  flex-direction: column;
  gap: 20px;
}

.right-column {
  position: sticky;
  top: 0;
  width: 400px;
  height: 100%;
  flex-shrink: 0; // 右侧固定宽度
}

// 页面头部
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .header-actions {
    .el-button {
      font-size: 14px;
    }
  }
}

// 表单区域
.form-section {
  padding: 20px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;

  .section-title {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .channel-tabs {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;

    .el-button {
      font-size: 14px;
      border-radius: 8px;

      .mr-1 {
        margin-right: 4px;
      }
    }
  }

  .form-content {
    .form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;
      gap: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .form-label {
        min-width: 60px;
        font-size: 14px;
        line-height: 32px;
        color: var(--el-text-color-regular);
        flex-shrink: 0;
      }

      .form-input {
        flex: 1;

        &.word-count-input {
          max-width: 120px;
        }

        :deep(.el-input__wrapper) {
          border-radius: 8px;
        }

        :deep(.el-textarea__inner) {
          border-radius: 8px;
        }
      }
    }
  }
}

// 生成按钮区域
.generate-section {
  display: flex;
  justify-content: center;
  margin: 20px 0;

  .generate-btn {
    min-width: 140px;
    padding: 12px 32px;
    font-size: 16px;
    border-radius: 12px;

    .mr-2 {
      margin-right: 8px;
    }
  }
}

// 内容展示区域
.content-section {
  padding: 20px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .section-actions {
      display: flex;
      gap: 12px;

      .el-button {
        font-size: 14px;

        .mr-1 {
          margin-right: 4px;
        }
      }
    }
  }

  .content-display {
    padding: 16px;
    margin-bottom: 20px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;

    .content-header {
      margin-bottom: 12px;

      .content-title {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }

      .highlight-text {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-color-primary);
      }
    }

    .content-body {
      .content-text {
        font-size: 14px;
        line-height: 1.6;
        color: var(--el-text-color-primary);
        white-space: pre-wrap;
      }
    }
  }

  .quick-generate {
    display: flex;
    justify-content: center;

    .quick-generate-btn {
      min-width: 160px;
      padding: 12px 24px;
      font-size: 16px;
      border-radius: 12px;
    }
  }
}

// 深色主题适配
.dark {
  .original-container {
    background: var(--el-bg-color);
  }
}
</style>
