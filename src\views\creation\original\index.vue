<template>
  <div id="original-generator-page">
    <!-- 左侧设置面板 -->
    <div class="settings-panel text-white rounded-2">
      <div class="panel-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>AI 文案生成</h1>
          <button @click="handleClear" class="clear-btn">清除</button>
        </div>

        <!-- 发布渠道选择 -->
        <div class="section">
          <label class="section-title">发布渠道</label>
          <div class="channel-tabs">
            <button
              v-for="channel in channels"
              :key="channel.key"
              :class="['channel-btn', { active: activeChannel === channel.key }]"
              @click="activeChannel = channel.key"
            >
              <Icon :icon="channel.icon" class="channel-icon" />
              {{ channel.label }}
            </button>
          </div>
        </div>

        <!-- 详细信息表单 -->
        <div class="section">
          <label class="section-title">详细信息</label>

          <div class="input-wrapper">
            <span class="mb-3">产品</span>
            <input
              type="text"
              v-model="formData.product"
              placeholder="如：耳机，一次只写一个产品词（必填）"
            />
          </div>

          <div class="input-wrapper">
            <span class="mb-3">客户</span>
            <input
              type="text"
              v-model="formData.customer"
              placeholder="如：上班族，一次只写一个产品词（必填）"
            />
          </div>

          <div class="input-wrapper">
            <span class="mb-3">其他</span>
            <textarea v-model="formData.other" placeholder="关于文案内容的补充信息"></textarea>
          </div>

          <div class="input-wrapper">
            <span class="mb-3">热词</span>
            <input type="text" v-model="formData.keywords" placeholder="填写热搜词" />
          </div>

          <div class="input-wrapper">
            <span class="mb-3">字数</span>
            <input type="text" v-model="formData.wordCount" placeholder="建议100-500字（必填）" />
          </div>
        </div>
      </div>

      <!-- 生成按钮 -->
      <GenerateButton
        @click="handleGenerate"
        :loading="isGenerating"
        text="立即生成"
        :cost="3"
        :loading-text="'生成中...'"
      />
    </div>

    <!-- 右侧结果区域 -->
    <div class="result-panel">
      <div v-if="error" class="error-state">
        <p>出错了：{{ error }}</p>
      </div>
      <div v-else-if="generatedContent" class="content-display">
        <!-- AI 原创标题和操作按钮 -->
        <div class="content-header">
          <h2>AI 原创</h2>
          <div class="content-actions">
            <button @click="handleManage" class="action-btn">
              <Icon icon="ep:user" />
              人员管理
            </button>
            <button @click="handleCopy" class="action-btn">复制</button>
          </div>
        </div>

        <!-- 生成的内容 -->
        <div class="generated-content">
          <div class="content-item">
            <span class="content-label">标题：</span>
            <div class="content-value title-content">{{ generatedContent.title }}</div>
          </div>
          <div class="content-item">
            <span class="content-label">文案：</span>
            <div class="content-value text-content">{{ generatedContent.content }}</div>
          </div>
        </div>

        <!-- 立即生成按钮 -->
        <div class="quick-generate-section">
          <GenerateButton @click="handleQuickGenerate" :loading="false" text="立即生成" :cost="3" />
        </div>
      </div>
      <div v-else class="initial-state text-center">
        <div class="placeholder-content">
          <p>以下是为您生成的营销内容，您可点击上方"立即生成"按钮再次生成</p>
        </div>
      </div>
    </div>

    <!-- 右侧AI聊天组件 -->
    <div class="chat-panel">
      <AiChatWidget
        title="AI设计师"
        welcome-message="Hi，我是你的AI设计师，让我们开始今天的创作吧！"
        :suggestions="aiSuggestions"
        placeholder="在这里输入问题"
        @message-sent="handleMessageSent"
        @message-received="handleMessageReceived"
        @error="handleChatError"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import AiChatWidget from '@/components/AiChatWidget/index.vue'
import GenerateButton from '@/components/GenerateButton/index.vue'

defineOptions({ name: 'CreationOriginal' })

// 发布渠道数据
const channels = ref([
  { key: 'douyin', label: '抖音', icon: 'ep:video-camera' },
  { key: 'xiaohongshu', label: '小红书', icon: 'ep:picture' }
])

// 当前选中的渠道
const activeChannel = ref('douyin')

// 表单数据
const formData = reactive({
  product: '耳机',
  customer: '上班族',
  other: '在地铁站中的使用场景，降噪',
  keywords: '耳机降噪',
  wordCount: '120'
})

// 生成状态
const isGenerating = ref(false)
const error = ref<string | null>(null)

// 生成的内容
const generatedContent = ref<{
  title: string
  content: string
} | null>(null)

// AI建议问题
const aiSuggestions = ref(['帮我提写营销文案', '科技公司品牌logo设计', '游戏图标设计'])

// 生成内容
const handleGenerate = async () => {
  if (!formData.product.trim()) {
    ElMessage.warning('请输入产品名称')
    return
  }

  isGenerating.value = true

  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 模拟生成的内容
    generatedContent.value = {
      title: '2025，未来已来！你准备好了吗？✨',
      content: `时间定格在这一刻——2025年7月22日16:03，世界正以前所未有的速度向前奔跑。
🚀 科技革新，重塑生活。
🎯 智能未来，触手可及。
🔥 你的每一步，都在定义未来的轨迹。
别让时间白白流逝，现在就是最好的开始！
🌟 拥抱改变，创造无限可能！🌟
#未来已来 #2025新元年 #改变从现在开始`
    }
  } catch (error) {
    ElMessage.error('生成失败，请重试')
  } finally {
    isGenerating.value = false
  }
}

// 快速生成
const handleQuickGenerate = () => {
  handleGenerate()
}

// 复制内容
const handleCopy = async () => {
  if (!generatedContent.value) return

  try {
    const textToCopy = `${generatedContent.value.title}\n\n${generatedContent.value.content}`
    await navigator.clipboard.writeText(textToCopy)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 清除表单
const handleClear = () => {
  formData.product = ''
  formData.customer = ''
  formData.other = ''
  formData.keywords = ''
  formData.wordCount = '120'
  generatedContent.value = null
  ElMessage.success('已清除所有内容')
}

// 人员管理
const handleManage = () => {
  ElMessage.info('人员管理功能开发中')
}

// AI聊天事件处理
const handleMessageSent = (message: string) => {
  console.log('消息已发送:', message)
}

const handleMessageReceived = (message: string) => {
  console.log('收到回复:', message)
}

const handleChatError = (error: Error) => {
  console.error('聊天错误:', error)
  ElMessage.error(`聊天出错: ${error.message}`)
}
</script>

<style scoped>
:root {
  --primary-bg: #1a1625;
  --secondary-bg: #2a253a;
  --panel-bg: #211e30;
  --text-color: #e0e0e0;
  --text-secondary-color: #a09cb0;
  --accent-color: #6c5ce7;
  --accent-hover-color: #5a4bd7;
  --border-color: #3a364f;
  --input-bg: #3a364f;
}

#original-generator-page {
  position: relative;
  display: flex;
  width: 100%;
  height: calc(100vh - 165px);
  gap: 16px;
}

/* 左侧设置面板 */
.settings-panel {
  display: flex;
  width: 296px;
  padding: 16px;
  background: rgb(255 255 255 / 5%);
  flex-direction: column;
}

.panel-content {
  flex-grow: 1;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.clear-btn {
  padding: 6px 12px;
  font-size: 14px;
  color: var(--accent-color);
  cursor: pointer;
  background: transparent;
  border: 1px solid var(--accent-color);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  color: white;
  background: var(--accent-color);
}

/* 区域样式 */
.section {
  margin-bottom: 32px;
}

.section-title {
  display: block;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: var(--text-color);
}

/* 发布渠道按钮 */
.channel-tabs {
  display: flex;
  gap: 8px;
}

.channel-btn {
  display: flex;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-secondary-color);
  cursor: pointer;
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  transition: all 0.3s;
  align-items: center;
  gap: 6px;
}

.channel-btn.active {
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
}

.channel-btn:not(.active):hover {
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
}

.channel-icon {
  font-size: 16px;
}

/* 输入框样式 */
.input-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 16px;
}

.input-wrapper span {
  width: 50px;
  padding-top: 8px;
  font-size: 14px;
  color: var(--text-secondary-color);
}

.input-wrapper input[type='text'],
.input-wrapper textarea {
  width: 100%;
  padding: 12px 16px;
  font-size: 14px;
  color: var(--text-color);
  background: transparent;
  border: 1px solid rgb(255 255 255 / 15%);
  border-radius: 10px;
  outline: none;
  transition: all 0.3s ease;
  flex-grow: 1;
  backdrop-filter: blur(5px);
}

.input-wrapper input[type='text']:focus,
.input-wrapper textarea:focus {
  background: rgb(255 255 255 / 12%);
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgb(108 92 231 / 20%);
}

.input-wrapper input[type='text']::placeholder,
.input-wrapper textarea::placeholder {
  color: var(--text-secondary-color);
  opacity: 0.7;
}

.input-wrapper textarea {
  height: 80px;
  font-family: inherit;
  resize: vertical;
}

/* 右侧结果区域 */
.result-panel {
  display: flex;
  padding: 40px;
  margin-left: 16px;
  overflow-y: auto;
  background-color: rgb(255 255 255 / 5%);
  border-radius: 8px;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.initial-state p,
.error-state p {
  font-size: 18px;
  color: #aeb9e1;
}

.error-state p {
  color: #ff6b6b;
}

/* 内容显示区域 */
.content-display {
  width: 100%;
  max-width: 800px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.content-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.content-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--accent-color);
  cursor: pointer;
  background: transparent;
  border: 1px solid var(--accent-color);
  border-radius: 6px;
  transition: all 0.3s ease;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  color: white;
  background: var(--accent-color);
}

/* 生成的内容 */
.generated-content {
  margin-bottom: 32px;
}

.content-item {
  margin-bottom: 24px;
}

.content-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary-color);
}

.content-value {
  padding: 16px;
  background: rgb(255 255 255 / 8%);
  border: 1px solid rgb(255 255 255 / 15%);
  border-radius: 10px;
}

.title-content {
  font-size: 16px;
  font-weight: 600;
  color: var(--accent-color);
}

.text-content {
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-color);
  white-space: pre-wrap;
}

.quick-generate-section {
  display: flex;
  justify-content: center;
}

/* 聊天面板 */
.chat-panel {
  width: 400px;
  height: 100%;
  flex-shrink: 0;
}
</style>
