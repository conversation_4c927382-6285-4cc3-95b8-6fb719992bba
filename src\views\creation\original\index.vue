<template>
  <div id="original-generator-page" class="text-white">
    <el-row :gutter="16" class="h-full">
      <!-- 左侧内容区域 -->
      <el-col :span="14">
        <el-col class="left-content">
          <!-- 上半部分：表单设置 -->
          <div class="settings-panel text-white rounded-2">
        <div class="panel-content">
          <!-- 页面标题 -->
          <div class="page-header">
            <h1>AI 文案生成</h1>
            <button @click="handleClear" class="clear-btn">清除</button>
          </div>

          <!-- 发布渠道选择 -->
          <div class="section">
            <label class="section-title">发布渠道</label>
            <div class="channel-tabs">
              <button
                v-for="channel in channels"
                :key="channel.key"
                :class="['channel-btn', { active: activeChannel === channel.key }]"
                @click="activeChannel = channel.key"
              >
                <Icon :icon="channel.icon" class="channel-icon" />
                {{ channel.label }}
              </button>
            </div>
          </div>

          <!-- 详细信息表单 -->
          <div class="section">
            <label class="section-title">详细信息</label>

            <div class="input-wrapper">
              <span class="mb-3">产品</span>
              <input
                type="text"
                v-model="formData.product"
                placeholder="如：耳机，一次只写一个产品词（必填）"
              />
            </div>

            <div class="input-wrapper">
              <span class="mb-3">客户</span>
              <input
                type="text"
                v-model="formData.customer"
                placeholder="如：上班族，一次只写一个产品词（必填）"
              />
            </div>

            <div class="input-wrapper">
              <span class="mb-3">其他</span>
              <textarea
                v-model="formData.other"
                placeholder="在地铁站中的使用场景，降噪"
              ></textarea>
            </div>

            <div class="input-wrapper">
              <span class="mb-3">热词</span>
              <input type="text" v-model="formData.keywords" placeholder="耳机降噪" />
            </div>

            <div class="input-wrapper">
              <span class="mb-3">字数</span>
              <input type="text" v-model="formData.wordCount" placeholder="120" />
            </div>
          </div>
        </div>

        <!-- 生成按钮 -->
        <GenerateButton
          @click="handleGenerate"
          :loading="isGenerating"
          text="立即生成"
          :cost="3"
          :loading-text="'生成中...'"
        />
      </div>

      <!-- 下半部分：AI原创内容展示 -->
      <div v-if="generatedContent" class="result-panel">
        <!-- AI 原创标题和操作按钮 -->
        <div class="content-header">
          <h2>AI 原创</h2>
          <div class="content-actions">
            <button @click="handleManage" class="action-btn">
              <Icon icon="ep:user" />
              人员管理
            </button>
            <button @click="handleCopy" class="action-btn">复制</button>
          </div>
        </div>
        <div class="placeholder-text">
          以下是为您生成的营销内容，您可点击上方"立即生成"按钮再次生成
        </div>

        <!-- 生成的内容 -->
        <div class="generated-content">
          <div class="content-display-text">
            <div class="title-line">
              <span class="title-label">标题：</span>
              <span class="title-text">{{ generatedContent.title }}</span>
            </div>
            <div class="content-line">
              <span class="content-label">文案：</span>
            </div>
            <div class="content-text">{{ generatedContent.content }}</div>
          </div>
        </div>


        </div>
      </el-col>

      <!-- 右侧AI聊天组件 -->
      <el-col :span="10">
        <div class="chat-panel">
          <AiChatWidget
            title="AI设计师"
            welcome-message="Hi，我是你的AI设计师，让我们开始今天的创作吧！"
            :suggestions="aiSuggestions"
            placeholder="在这里输入问题"
            @message-sent="handleMessageSent"
            @message-received="handleMessageReceived"
            @error="handleChatError"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import AiChatWidget from '@/components/AiChatWidget/index.vue'
import GenerateButton from '@/components/GenerateButton/index.vue'
import { generateOriginalContent, type OriginalContentRequest } from '@/api/ai/workflow'

defineOptions({ name: 'CreationOriginal' })

// 发布渠道数据
const channels = ref([
  { key: '抖音', label: '抖音', icon: 'ep:video-camera' },
  { key: '小红书', label: '小红书', icon: 'ep:picture' }
])

// 当前选中的渠道
const activeChannel = ref('抖音')

// 表单数据
const formData = reactive({
  product: '耳机',
  customer: '上班族',
  other: '在地铁站中的使用场景，降噪',
  keywords: '耳机降噪',
  wordCount: '120'
})

// 生成状态
const isGenerating = ref(false)

// 生成的内容
const generatedContent = ref<{
  title: string
  content: string
} | null>(null)

// AI建议问题
const aiSuggestions = ref(['帮我提写营销文案', '科技公司品牌logo设计', '游戏图标设计'])

// 生成文案
const handleGenerate = async () => {
  if (isGenerating.value) return

  // 验证必填字段
  if (!formData.product.trim()) {
    ElMessage.warning('请输入产品名称')
    return
  }

  if (!formData.customer.trim()) {
    ElMessage.warning('请输入目标客户')
    return
  }

  isGenerating.value = true

  try {
    // 构建API请求参数
    const requestParams: OriginalContentRequest = {
      platform: activeChannel.value,
      product: formData.product.trim(),
      customer: formData.customer.trim(),
      remark: formData.other.trim(),
      hot_world: formData.keywords.trim(),
      number: formData.wordCount.trim() || '120'
    }

    // 调用AI原创文案生成API
    const result = await generateOriginalContent(requestParams)

    // 设置生成的内容
    generatedContent.value = {
      title: result.title,
      content: result.content
    }

    ElMessage.success('文案生成成功！')
  } catch (error) {
    console.error('生成失败:', error)
    ElMessage.error(error instanceof Error ? error.message : '生成失败，请重试')
  } finally {
    isGenerating.value = false
  }
}

// 复制内容
const handleCopy = async () => {
  if (!generatedContent.value) return

  try {
    const textToCopy = `${generatedContent.value.title}\n\n${generatedContent.value.content}`
    await navigator.clipboard.writeText(textToCopy)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 清除表单
const handleClear = () => {
  formData.product = ''
  formData.customer = ''
  formData.other = ''
  formData.keywords = ''
  formData.wordCount = '120'
  generatedContent.value = null
  ElMessage.success('已清除所有内容')
}

// 人员管理
const handleManage = () => {
  ElMessage.info('人员管理功能开发中')
}

// AI聊天事件处理
const handleMessageSent = (message: string) => {
  console.log('消息已发送:', message)
}

const handleMessageReceived = (message: string) => {
  console.log('收到回复:', message)
}

const handleChatError = (error: Error) => {
  console.error('聊天错误:', error)
  ElMessage.error(`聊天出错: ${error.message}`)
}
</script>

<style scoped>
:root {
  --primary-bg: #1a1625;
  --secondary-bg: #2a253a;
  --panel-bg: #211e30;
  --text-color: #e0e0e0;
  --text-secondary-color: #a09cb0;
  --accent-color: #6c5ce7;
  --accent-hover-color: #5a4bd7;
  --border-color: #3a364f;
  --input-bg: #3a364f;
}

#original-generator-page {
  height: calc(100vh - 165px);
  padding: 0;
}

/* 左侧内容区域 */
.left-content {
  display: flex;
  height: 100%;
  flex-direction: column;
  gap: 16px;
}

/* 左侧设置面板 */
.settings-panel {
  display: flex;
  padding: 16px;
  background: rgb(255 255 255 / 5%);
  border-radius: 8px;
  flex-direction: column;
}

.panel-content {
  flex-grow: 1;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.clear-btn {
  padding: 6px 12px;
  font-size: 14px;
  color: var(--accent-color);
  cursor: pointer;
  background: transparent;
  border: 1px solid var(--accent-color);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  color: white;
  background: var(--accent-color);
}

/* 区域样式 */
.section {
  margin-bottom: 32px;
}

.section-title {
  display: block;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: var(--text-color);
}

/* 发布渠道按钮 */
.channel-tabs {
  display: flex;
  gap: 8px;
}

.channel-btn {
  display: flex;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-secondary-color);
  cursor: pointer;
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  transition: all 0.3s;
  align-items: center;
  gap: 6px;
}

.channel-btn.active {
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
}

.channel-btn:not(.active):hover {
  color: white;
  background: linear-gradient(90deg, #675dff 0%, #bf78e6 100%);
  border: none;
}

.channel-icon {
  font-size: 16px;
}

/* 输入框样式 */
.input-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 16px;
}

.input-wrapper span {
  width: 50px;
  padding-top: 8px;
  font-size: 14px;
  color: var(--text-secondary-color);
}

.input-wrapper input[type='text'],
.input-wrapper textarea {
  width: 100%;
  padding: 12px 16px;
  font-size: 14px;
  color: var(--text-color);
  background: transparent;
  border: 1px solid rgb(255 255 255 / 15%);
  border-radius: 10px;
  outline: none;
  transition: all 0.3s ease;
  flex-grow: 1;
  backdrop-filter: blur(5px);
}

.input-wrapper input[type='text']:focus,
.input-wrapper textarea:focus {
  border-color: #6c5ce7;
  outline: none;
  box-shadow: 0 0 0 3px rgb(108 92 231 / 10%);
}

.input-wrapper input[type='text']::placeholder,
.input-wrapper textarea::placeholder {
  color: var(--text-secondary-color);
  opacity: 0.7;
}

.input-wrapper textarea {
  height: 80px;
  font-family: inherit;
  resize: vertical;
}

/* 结果面板 */
.result-panel {
  padding: 16px;
  background-color: rgb(255 255 255 / 5%);
  border-radius: 8px;
  flex-grow: 1;
}

.placeholder-text {
  margin-bottom: 16px;
  font-size: 14px;
  color: #aeb9e1;
  text-align: center;
}

/* 内容头部 */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.content-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.content-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  padding: 6px 12px;
  font-size: 14px;
  color: var(--accent-color);
  cursor: pointer;
  background: transparent;
  border: 1px solid var(--accent-color);
  border-radius: 6px;
  transition: all 0.3s ease;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  color: white;
  background: var(--accent-color);
}

/* 生成的内容 */
.generated-content {
  margin-top: 16px;
}

.content-display-text {
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-color);
  background: rgb(255 255 255 / 3%);
  border: 1px solid rgb(255 255 255 / 10%);
  border-radius: 8px;
}

.title-line {
  display: flex;
  margin-bottom: 12px;
  align-items: baseline;
}

.title-label {
  font-weight: 500;
  color: var(--text-color);
  flex-shrink: 0;
}

.title-text {
  margin-left: 4px;
  font-weight: 600;
  color: var(--text-color);
}

.content-line {
  margin-bottom: 8px;
}

.content-label {
  font-weight: 500;
  color: var(--text-color);
}

.content-text {
  line-height: 1.8;
  color: var(--text-color);
  white-space: pre-wrap;
}

.quick-generate-section {
  display: flex;
  justify-content: center;
}

/* 聊天面板 */
.chat-panel {
  width: 400px;
  height: 100%;
  flex-shrink: 0;
}
</style>
